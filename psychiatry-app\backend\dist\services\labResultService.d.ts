import { CreateLabResultData, ApiResponse, PaginatedResponse } from '@/types';
export declare class LabResultService {
    static createLabResult(data: CreateLabResultData, createdBy: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        labResult: any;
    }>>;
    static getLabResults(query: {
        page?: string;
        limit?: string;
        patientId?: string;
        testType?: string;
        status?: string;
        dateFrom?: string;
        dateTo?: string;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }, userId: string, userRole: string): Promise<PaginatedResponse<any>>;
    static getLabResultById(id: string, userId: string, userRole: string): Promise<ApiResponse<{
        labResult: any;
    }>>;
    static getPatientLabResults(patientId: string, userId: string, userRole: string, query?: {
        testType?: string;
        limit?: number;
        dateFrom?: string;
        dateTo?: string;
    }): Promise<ApiResponse<{
        labResults: any[];
    }>>;
    static getLabResultTrends(patientId: string, testType: string, userId: string, userRole: string, dateFrom?: string, dateTo?: string): Promise<ApiResponse<{
        trends: any[];
    }>>;
    static deleteLabResult(id: string, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<null>>;
    private static createAuditLog;
}
//# sourceMappingURL=labResultService.d.ts.map