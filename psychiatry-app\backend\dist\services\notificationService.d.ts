import { CreateNotificationData, ApiResponse, PaginatedResponse } from '@/types';
export declare class NotificationService {
    static createNotification(data: CreateNotificationData, createdBy: string): Promise<ApiResponse<{
        notification: any;
    }>>;
    static getNotifications(query: {
        page?: string;
        limit?: string;
        recipientId?: string;
        type?: string;
        status?: string;
        priority?: string;
        channel?: string;
        dateFrom?: string;
        dateTo?: string;
    }, userId: string, userRole: string): Promise<PaginatedResponse<any>>;
    static markAsRead(id: string, userId: string, userRole: string): Promise<ApiResponse<null>>;
    static createAppointmentReminders(appointmentId: string, createdBy: string): Promise<ApiResponse<{
        reminders: any[];
    }>>;
    static sendLabResultNotification(labResultId: string, createdBy: string): Promise<ApiResponse<{
        notification: any;
    }>>;
    static processScheduledNotifications(): Promise<ApiResponse<{
        processed: number;
    }>>;
    private static sendNotification;
    private static sendEmailNotification;
    private static sendSMSNotification;
    static getNotificationStats(userId: string, userRole: string): Promise<ApiResponse<{
        stats: any;
    }>>;
}
//# sourceMappingURL=notificationService.d.ts.map