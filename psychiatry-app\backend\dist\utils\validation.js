"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePatientSchema = exports.createPatientSchema = exports.createNotificationSchema = exports.updateLabResultSchema = exports.createLabResultSchema = exports.loginSchema = exports.registerSchema = exports.updateAppointmentSchema = exports.createAppointmentSchema = void 0;
const zod_1 = require("zod");
exports.createAppointmentSchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid('Invalid patient ID'),
    providerId: zod_1.z.string().uuid('Invalid provider ID'),
    date: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
    duration: zod_1.z.number().min(15, 'Duration must be at least 15 minutes').max(480, 'Duration cannot exceed 8 hours'),
    type: zod_1.z.enum([
        'INITIAL_CONSULTATION',
        'FOLLOW_UP',
        'THERAPY_SESSION',
        'MEDICATION_REVIEW',
        'CRISIS_INTERVENTION',
        'GROUP_THERAPY',
        'FAMILY_THERAPY',
        'PSYCHOLOGICAL_TESTING',
        'OTHER',
    ]),
    status: zod_1.z.enum(['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'RESCHEDULED']).optional(),
    title: zod_1.z.string().max(200).optional(),
    description: zod_1.z.string().max(1000).optional(),
    location: zod_1.z.string().max(200).optional(),
    isVirtual: zod_1.z.boolean().optional(),
    virtualMeetingUrl: zod_1.z.string().url().optional(),
    notes: zod_1.z.string().max(2000).optional(),
    recurringAppointmentId: zod_1.z.string().uuid().optional(),
});
exports.updateAppointmentSchema = exports.createAppointmentSchema.partial();
exports.registerSchema = zod_1.z.object({
    username: zod_1.z.string()
        .min(3, 'Username must be at least 3 characters')
        .max(50, 'Username must be less than 50 characters')
        .regex(/^[a-zA-Z0-9._-]+$/, 'Username can only contain letters, numbers, dots, underscores, and hyphens'),
    email: zod_1.z.string()
        .email('Invalid email format')
        .max(255, 'Email must be less than 255 characters'),
    password: zod_1.z.string()
        .min(8, 'Password must be at least 8 characters'),
    firstName: zod_1.z.string()
        .min(1, 'First name is required')
        .max(100, 'First name must be less than 100 characters'),
    lastName: zod_1.z.string()
        .min(1, 'Last name is required')
        .max(100, 'Last name must be less than 100 characters'),
    role: zod_1.z.enum(['ADMIN', 'CLINICIAN', 'STAFF']).optional(),
});
exports.loginSchema = zod_1.z.object({
    username: zod_1.z.string()
        .min(1, 'Username or email is required'),
    password: zod_1.z.string()
        .min(1, 'Password is required'),
});
exports.createLabResultSchema = zod_1.z.object({
    patientId: zod_1.z.string().uuid(),
    testType: zod_1.z.enum([
        'CBC',
        'METABOLIC_PANEL',
        'LIPID_PANEL',
        'THYROID',
        'LIVER_FUNCTION',
        'KIDNEY_FUNCTION',
        'VITAMIN_LEVELS',
        'DRUG_SCREEN',
        'CARDIAC_MARKERS',
        'INFLAMMATORY',
        'COAGULATION',
        'URINALYSIS',
        'HEMOGLOBIN_A1C',
        'OTHER',
    ]),
    testDate: zod_1.z.string().datetime(),
    orderedBy: zod_1.z.string(),
    labName: zod_1.z.string().optional(),
    results: zod_1.z.record(zod_1.z.any()),
    normalRanges: zod_1.z.record(zod_1.z.any()).optional(),
    flags: zod_1.z.record(zod_1.z.any()).optional(),
    notes: zod_1.z.string().optional(),
    status: zod_1.z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'AMENDED']).optional(),
});
exports.updateLabResultSchema = exports.createLabResultSchema.partial();
exports.createNotificationSchema = zod_1.z.object({
    recipientId: zod_1.z.string().uuid(),
    type: zod_1.z.enum([
        'APPOINTMENT_REMINDER',
        'LAB_RESULT_AVAILABLE',
        'APPOINTMENT_CANCELLED',
        'APPOINTMENT_RESCHEDULED',
        'LAB_RESULT_CRITICAL',
        'SYSTEM_NOTIFICATION',
        'WELCOME',
        'PASSWORD_RESET',
        'ACCOUNT_LOCKED',
        'OTHER'
    ]),
    title: zod_1.z.string(),
    message: zod_1.z.string(),
    priority: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
    scheduledFor: zod_1.z.string().datetime().optional(),
    patientId: zod_1.z.string().uuid().optional(),
    appointmentId: zod_1.z.string().uuid().optional(),
    labResultId: zod_1.z.string().uuid().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    channel: zod_1.z.enum(['IN_APP', 'EMAIL', 'SMS']).optional(),
});
exports.createPatientSchema = zod_1.z.object({
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    dateOfBirth: zod_1.z.string().datetime(),
    gender: zod_1.z.enum(['MALE', 'FEMALE', 'NON_BINARY', 'PREFER_NOT_TO_SAY', 'OTHER']),
    phone: zod_1.z.string().optional(),
    email: zod_1.z.string().email().optional(),
    address: zod_1.z.object({
        street: zod_1.z.string(),
        city: zod_1.z.string(),
        state: zod_1.z.string(),
        zipCode: zod_1.z.string(),
        country: zod_1.z.string().optional(),
    }).optional(),
    occupation: zod_1.z.string().optional(),
    education: zod_1.z.enum(['ELEMENTARY', 'HIGH_SCHOOL', 'SOME_COLLEGE', 'BACHELORS', 'MASTERS', 'DOCTORATE', 'PROFESSIONAL', 'OTHER']).optional(),
    maritalStatus: zod_1.z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED', 'DOMESTIC_PARTNERSHIP', 'OTHER']).optional(),
    emergencyContact: zod_1.z.object({
        name: zod_1.z.string(),
        phone: zod_1.z.string(),
        relationship: zod_1.z.string(),
        email: zod_1.z.string().email().optional(),
    }).optional(),
    insuranceInfo: zod_1.z.object({
        provider: zod_1.z.string(),
        policyNumber: zod_1.z.string(),
        groupNumber: zod_1.z.string().optional(),
        subscriberName: zod_1.z.string().optional(),
        effectiveDate: zod_1.z.string().optional(),
        expirationDate: zod_1.z.string().optional(),
    }).optional(),
    medicalHistory: zod_1.z.string().optional(),
    allergies: zod_1.z.string().optional(),
    currentMeds: zod_1.z.string().optional(),
    notes: zod_1.z.string().optional(),
});
exports.updatePatientSchema = exports.createPatientSchema.partial().extend({
    isActive: zod_1.z.boolean().optional(),
});
//# sourceMappingURL=validation.js.map