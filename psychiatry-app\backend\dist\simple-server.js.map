{"version": 3, "file": "simple-server.js", "sourceRoot": "", "sources": ["../src/simple-server.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,gDAAwB;AAExB,oDAA4B;AAC5B,2CAA8C;AAC9C,oDAA4B;AAG5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AACtB,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC;AACtC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAGlC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,GAAE,CAAC,CAAC;AAChB,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AACzC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAQ/D,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpC,IAAI,CAAC;QAEH,MAAM,MAAM,CAAC,SAAS,CAAA,UAAU,CAAC;QAEjC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,mBAAmB;YAC5B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ;YACjC,QAAQ,EAAE,WAAW;SACtB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,4BAA4B;YACrC,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACnB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,mCAAmC;QAC5C,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE;YACT,MAAM,EAAE,aAAa;YACrB,GAAG,EAAE,UAAU;YACf,MAAM,EAAE,kBAAkB;YAC1B,KAAK,EAAE,gBAAgB;YACvB,UAAU,EAAE,iBAAiB;YAC7B,QAAQ,EAAE,mBAAmB;YAC7B,aAAa,EAAE,oBAAoB;SACpC;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAGH,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACzC,IAAI,CAAC;QACH,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;YAChC,QAAQ,EAAE,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;YACtC,UAAU,EAAE,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE;YAC1C,YAAY,EAAE,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;YAC9C,aAAa,EAAE,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE;YAChD,qBAAqB,EAAE,MAAM,MAAM,CAAC,oBAAoB,CAAC,KAAK,EAAE;SACjE,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,0BAA0B;YACnC,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,sBAAsB;YAC/B,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACvC,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;YAChC,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxC,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,GAAG,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAExF,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,cAAc,GAAG,MAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAEvD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,IAAI,EAAE;gBACJ,QAAQ;gBACR,KAAK;gBACL,QAAQ,EAAE,cAAc;gBACxB,SAAS;gBACT,QAAQ;gBACR,IAAI;aACL;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,2BAA2B;SACrC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;YAChC,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC1C,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,KAAK,EAAE;gBACL,SAAS,EAAE,KAAK;aACjB;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,QAAQ;SACf,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0BAA0B;YACnC,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3C,IAAI,CAAC;QACH,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,WAAW,EACX,MAAM,EACN,KAAK,EACL,KAAK,EACL,SAAS,GAAG,QAAQ,EACrB,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;YACvD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAClD,MAAM,SAAS,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAE/F,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,IAAI,EAAE;gBACJ,SAAS;gBACT,SAAS;gBACT,QAAQ;gBACR,WAAW,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC;gBAClC,MAAM;gBACN,KAAK,EAAE,KAAK,IAAI,IAAI;gBACpB,KAAK,EAAE,KAAK,IAAI,IAAI;gBACpB,SAAS;aACV;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,8BAA8B;SACxC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,0BAA0B;YACnC,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACnB,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,oBAAoB;QAC7B,IAAI,EAAE,GAAG,CAAC,WAAW;KACtB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAGH,GAAG,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;IACpD,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC/B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACnB,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,uBAAuB;QAChC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;KACvF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAGH,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;IACnC,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC;IACxD,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,qCAAqC,IAAI,SAAS,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,MAAM,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,sCAAsC,IAAI,cAAc,CAAC,CAAC;AACxE,CAAC,CAAC,CAAC;AAGH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;IAC/B,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC3B,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;IAC9B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC3B,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,kBAAe,GAAG,CAAC"}