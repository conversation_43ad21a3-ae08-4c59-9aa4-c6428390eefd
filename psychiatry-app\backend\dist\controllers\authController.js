"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const zod_1 = require("zod");
const authService_1 = require("@/services/authService");
const validation_1 = require("../utils/validation");
const refreshTokenSchema = zod_1.z.object({
    refreshToken: zod_1.z.string()
        .min(1, 'Refresh token is required'),
});
const changePasswordSchema = zod_1.z.object({
    currentPassword: zod_1.z.string()
        .min(1, 'Current password is required'),
    newPassword: zod_1.z.string()
        .min(8, 'New password must be at least 8 characters'),
});
class AuthController {
    static async register(req, res, next) {
        try {
            const validatedData = validation_1.registerSchema.parse(req.body);
            const result = await authService_1.AuthService.register(validatedData);
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async login(req, res, next) {
        try {
            const validatedData = validation_1.loginSchema.parse(req.body);
            const ipAddress = req.ip;
            const userAgent = req.get('User-Agent');
            const result = await authService_1.AuthService.login(validatedData, ipAddress, userAgent);
            res.cookie('refreshToken', result.data?.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 7 * 24 * 60 * 60 * 1000,
            });
            res.status(200).json({
                success: result.success,
                data: {
                    user: result.data?.user,
                    accessToken: result.data?.accessToken,
                },
                message: result.message,
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async refreshToken(req, res, next) {
        try {
            const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
            if (!refreshToken) {
                return res.status(401).json({
                    success: false,
                    error: 'Refresh token is required',
                });
            }
            const result = await authService_1.AuthService.refreshToken(refreshToken);
            res.cookie('refreshToken', result.data?.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 7 * 24 * 60 * 60 * 1000,
            });
            res.status(200).json({
                success: result.success,
                data: {
                    accessToken: result.data?.accessToken,
                },
                message: result.message,
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async logout(req, res, next) {
        try {
            const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
            if (refreshToken) {
                await authService_1.AuthService.logout(refreshToken);
            }
            res.clearCookie('refreshToken');
            res.status(200).json({
                success: true,
                data: null,
                message: 'Logout successful',
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getCurrentUser(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const result = await authService_1.AuthService.getCurrentUser(req.user.id);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async changePassword(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const validatedData = changePasswordSchema.parse(req.body);
            const result = await authService_1.AuthService.changePassword(req.user.id, validatedData.currentPassword, validatedData.newPassword);
            res.clearCookie('refreshToken');
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=authController.js.map