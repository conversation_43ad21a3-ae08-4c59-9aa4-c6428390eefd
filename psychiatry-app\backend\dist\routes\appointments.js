"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const appointmentController_1 = require("@/controllers/appointmentController");
const auth_1 = require("@/middleware/auth");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/stats', appointmentController_1.AppointmentController.getAppointmentStats);
router.get('/providers/:providerId/availability', appointmentController_1.AppointmentController.getProviderAvailability);
router.get('/', appointmentController_1.AppointmentController.getAppointments);
router.post('/', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), appointmentController_1.AppointmentController.createAppointment);
router.get('/:id', appointmentController_1.AppointmentController.getAppointmentById);
router.put('/:id', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), appointmentController_1.AppointmentController.updateAppointment);
router.post('/:id/cancel', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), appointmentController_1.AppointmentController.cancelAppointment);
exports.default = router;
//# sourceMappingURL=appointments.js.map