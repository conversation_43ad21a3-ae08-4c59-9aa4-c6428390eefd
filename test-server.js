const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.json({ success: true, message: 'Test server is running' });
});

app.get('/api/analytics/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      analytics: {
        patients: { total: 10, newThisWeek: 2, newThisMonth: 5 },
        appointments: { total: 20, today: 3, upcoming: 8 },
        labResults: { total: 15, pending: 2, flagged: 1 },
        system: { totalUsers: 5, activeUsers: 3 }
      }
    }
  });
});

const server = app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
});

process.on('SIGINT', () => {
  console.log('Shutting down test server');
  server.close();
  process.exit(0);
});
