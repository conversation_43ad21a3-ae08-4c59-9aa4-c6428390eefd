const http = require('http');

function testEndpoint(path, description) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`\n=== ${description} ===`);
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response:`, data);
        
        try {
          const parsed = JSON.parse(data);
          console.log(`Success: ${parsed.success}`);
          if (parsed.data) {
            console.log(`Data keys:`, Object.keys(parsed.data));
          }
        } catch (e) {
          console.log('Failed to parse JSON response');
        }
        
        resolve({ status: res.statusCode, data });
      });
    });

    req.on('error', (err) => {
      console.log(`\n=== ${description} ===`);
      console.log(`Error: ${err.message}`);
      console.log(`Error code: ${err.code}`);
      console.log(`Error details:`, err);
      reject(err);
    });

    req.setTimeout(5000, () => {
      console.log(`\n=== ${description} ===`);
      console.log('Request timeout');
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.end();
  });
}

async function runTests() {
  console.log('Testing Analytics Endpoints...\n');
  
  try {
    // Test health endpoint first
    await testEndpoint('/health', 'Health Check');
    
    // Test dashboard analytics
    await testEndpoint('/api/analytics/dashboard', 'Dashboard Analytics');
    
    // Test patient analytics with date range
    const today = new Date().toISOString().split('T')[0];
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    await testEndpoint(`/api/analytics/patients?from=${thirtyDaysAgo}&to=${today}`, 'Patient Analytics');
    await testEndpoint(`/api/analytics/appointments?from=${thirtyDaysAgo}&to=${today}`, 'Appointment Analytics');
    await testEndpoint(`/api/analytics/lab-results?from=${thirtyDaysAgo}&to=${today}`, 'Lab Results Analytics');
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
  
  console.log('\n=== Tests Complete ===');
}

runTests();
