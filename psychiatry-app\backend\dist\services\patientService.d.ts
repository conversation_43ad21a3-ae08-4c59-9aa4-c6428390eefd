import { CreatePatientData, UpdatePatientData, PatientQuery, ApiResponse, PaginatedResponse } from '@/types';
export declare class PatientService {
    private static generatePatientId;
    static createPatient(data: CreatePatientData, createdBy: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        patient: any;
    }>>;
    static getPatients(query: PatientQuery, userId: string, userRole: string): Promise<PaginatedResponse<any>>;
    static getPatientById(id: string, userId: string, userRole: string): Promise<ApiResponse<{
        patient: any;
    }>>;
    static updatePatient(id: string, data: UpdatePatientData, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        patient: any;
    }>>;
    static deletePatient(id: string, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<null>>;
    private static createAuditLog;
}
//# sourceMappingURL=patientService.d.ts.map