import { Response, NextFunction } from 'express';
import { AuthRequest } from '@/types';
export declare class AnalyticsController {
    static getDashboardAnalytics(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getPatientAnalytics(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getAppointmentAnalytics(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getLabResultAnalytics(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getSystemAnalytics(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=analyticsController.d.ts.map