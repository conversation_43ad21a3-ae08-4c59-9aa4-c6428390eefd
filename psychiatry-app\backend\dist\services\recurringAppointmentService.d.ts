import { CreateRecurringAppointmentData, ApiResponse } from '@/types';
export declare class RecurringAppointmentService {
    static createRecurringAppointment(data: CreateRecurringAppointmentData, createdBy: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        recurringAppointment: any;
        appointments: any[];
    }>>;
    static generateAppointments(recurringAppointmentId: string, createdBy: string): Promise<any[]>;
    private static getNextOccurrence;
    static updateRecurringAppointment(id: string, data: Partial<CreateRecurringAppointmentData>, updateFutureAppointments: boolean, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        recurringAppointment: any;
    }>>;
    static cancelRecurringAppointment(id: string, reason: string, cancelFutureAppointments: boolean, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        cancelledAppointments: number;
    }>>;
    static getRecurringAppointments(query: {
        page?: string;
        limit?: string;
        patientId?: string;
        providerId?: string;
        isActive?: string;
    }, userId: string, userRole: string): Promise<any>;
    private static createAuditLog;
}
//# sourceMappingURL=recurringAppointmentService.d.ts.map