import { TokenPayload, RefreshTokenPayload } from '@/types';
export declare const generateAccessToken: (payload: {
    userId: string;
    username: string;
    role: string;
}) => string;
export declare const generateRefreshToken: (payload: {
    userId: string;
    tokenId: string;
}) => string;
export declare const verifyAccessToken: (token: string) => TokenPayload;
export declare const verifyRefreshToken: (token: string) => RefreshTokenPayload;
export declare const extractTokenFromHeader: (authHeader: string | undefined) => string | null;
export declare const getTokenExpiration: (token: string) => Date | null;
export declare const isTokenExpired: (token: string) => boolean;
//# sourceMappingURL=jwt.d.ts.map