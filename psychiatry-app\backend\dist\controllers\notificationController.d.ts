import { Response, NextFunction } from 'express';
import { AuthRequest } from '@/types';
export declare class NotificationController {
    static createNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getNotifications(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static markAsRead(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static createAppointmentReminders(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static sendLabResultNotification(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static processScheduledNotifications(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getNotificationStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=notificationController.d.ts.map