"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const analyticsController_1 = require("@/controllers/analyticsController");
const auth_1 = require("@/middleware/auth");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/dashboard', analyticsController_1.AnalyticsController.getDashboardAnalytics);
router.get('/patients', analyticsController_1.AnalyticsController.getPatientAnalytics);
router.get('/appointments', analyticsController_1.AnalyticsController.getAppointmentAnalytics);
router.get('/lab-results', analyticsController_1.AnalyticsController.getLabResultAnalytics);
router.get('/system', (0, auth_1.authorize)(['ADMIN']), analyticsController_1.AnalyticsController.getSystemAnalytics);
exports.default = router;
//# sourceMappingURL=analytics.js.map