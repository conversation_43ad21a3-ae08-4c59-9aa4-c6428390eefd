import { CreateAppointmentData, UpdateAppointmentData, ApiResponse, PaginatedResponse } from '@/types';
export declare class AppointmentService {
    private static isTimeSlotAvailable;
    static createAppointment(data: CreateAppointmentData, createdBy: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        appointment: any;
    }>>;
    static getAppointments(query: {
        page?: string;
        limit?: string;
        patientId?: string;
        providerId?: string;
        status?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }, userId: string, userRole: string): Promise<PaginatedResponse<any>>;
    static getAppointmentById(id: string, userId: string, userRole: string): Promise<ApiResponse<{
        appointment: any;
    }>>;
    static updateAppointment(id: string, data: UpdateAppointmentData, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<{
        appointment: any;
    }>>;
    static cancelAppointment(id: string, reason: string, userId: string, userRole: string, auditData?: {
        ipAddress?: string;
        userAgent?: string;
    }): Promise<ApiResponse<null>>;
    static getProviderAvailability(providerId: string, date: string, userId: string, userRole: string): Promise<ApiResponse<{
        availability: any[];
    }>>;
    private static createAuditLog;
}
//# sourceMappingURL=appointmentService.d.ts.map