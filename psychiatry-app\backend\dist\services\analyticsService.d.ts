import { ApiResponse } from '@/types';
export declare class AnalyticsService {
    static getDashboardAnalytics(userId: string, userRole: string): Promise<ApiResponse<{
        analytics: any;
    }>>;
    static getPatientAnalytics(dateRange: {
        from: string;
        to: string;
    }, userId: string, userRole: string): Promise<ApiResponse<{
        analytics: any;
    }>>;
    static getAppointmentAnalytics(dateRange: {
        from: string;
        to: string;
    }, userId: string, userRole: string): Promise<ApiResponse<{
        analytics: any;
    }>>;
    static getLabResultAnalytics(dateRange: {
        from: string;
        to: string;
    }, userId: string, userRole: string): Promise<ApiResponse<{
        analytics: any;
    }>>;
    static getSystemAnalytics(dateRange: {
        from: string;
        to: string;
    }, userId: string, userRole: string): Promise<ApiResponse<{
        analytics: any;
    }>>;
}
//# sourceMappingURL=analyticsService.d.ts.map