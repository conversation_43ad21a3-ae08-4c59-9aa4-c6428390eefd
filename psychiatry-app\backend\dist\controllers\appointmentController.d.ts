import { Response, NextFunction } from 'express';
import { AuthRequest } from '@/types';
export declare class AppointmentController {
    static createAppointment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getAppointments(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getAppointmentById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static updateAppointment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static cancelAppointment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getProviderAvailability(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getAppointmentStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=appointmentController.d.ts.map