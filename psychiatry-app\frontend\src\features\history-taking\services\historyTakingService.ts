import api, { handleApiError } from '../../../lib/api';
import type { AssessmentSession } from '../types';

export const historyTakingService = {
  /**
   * Save a psychiatric assessment session
   * @param patientId The patient ID
   * @param session The assessment session data to save
   * @returns The saved assessment session with ID
   */
  async saveAssessmentSession(patientId: string, session: Partial<AssessmentSession>): Promise<AssessmentSession> {
    try {
      // Validate patient ID before making the call
      if (!patientId || patientId.trim() === '') {
        throw new Error('Patient ID is required to save assessment session');
      }

      const response = await api.post(`/api/patients/${patientId}/assessment-sessions`, session);
      return response.data.data as AssessmentSession;
    } catch (error) {
      console.error('Error saving assessment session:', error);
      const errorMessage = handleApiError(error);
      throw new Error(`Failed to save assessment session: ${errorMessage}`);
    }
  },

  /**
   * Get assessment sessions for a patient
   * @param patientId The patient ID
   * @returns Array of assessment sessions
   */
  async getPatientAssessmentSessions(patientId: string): Promise<AssessmentSession[]> {
    try {
      // Validate patient ID before making the call
      if (!patientId || patientId.trim() === '') {
        throw new Error('Patient ID is required to fetch assessment sessions');
      }

      const response = await api.get(`/api/patients/${patientId}/assessment-sessions`);
      return response.data.data as AssessmentSession[];
    } catch (error) {
      console.error('Error fetching assessment sessions:', error);
      const errorMessage = handleApiError(error);
      throw new Error(`Failed to fetch assessment sessions: ${errorMessage}`);
    }
  },

  /**
   * Get a specific assessment session by ID
   * @param sessionId The assessment session ID
   * @returns The assessment session
   */
  async getAssessmentSessionById(sessionId: string): Promise<AssessmentSession> {
    try {
      // Validate session ID before making the call
      if (!sessionId || sessionId.trim() === '') {
        throw new Error('Session ID is required to fetch assessment session');
      }

      const response = await api.get(`/api/assessment-sessions/${sessionId}`);
      return response.data.data as AssessmentSession;
    } catch (error) {
      console.error('Error fetching assessment session:', error);
      const errorMessage = handleApiError(error);
      throw new Error(`Failed to fetch assessment session: ${errorMessage}`);
    }
  }
};

export default historyTakingService;