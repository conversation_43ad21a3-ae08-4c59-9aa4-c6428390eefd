import { LoginCredentials, RegisterData, AuthenticatedUser, ApiResponse } from '@/types';
export declare class AuthService {
    static register(data: RegisterData): Promise<ApiResponse<{
        user: AuthenticatedUser;
    }>>;
    static login(credentials: LoginCredentials, ipAddress?: string, userAgent?: string): Promise<ApiResponse<{
        user: AuthenticatedUser;
        accessToken: string;
        refreshToken: string;
    }>>;
    static refreshToken(refreshToken: string): Promise<ApiResponse<{
        accessToken: string;
        refreshToken: string;
    }>>;
    static logout(refreshToken: string): Promise<ApiResponse<null>>;
    static getCurrentUser(userId: string): Promise<ApiResponse<{
        user: AuthenticatedUser;
    }>>;
    static changePassword(userId: string, currentPassword: string, newPassword: string): Promise<ApiResponse<null>>;
    private static createAuditLog;
}
//# sourceMappingURL=authService.d.ts.map