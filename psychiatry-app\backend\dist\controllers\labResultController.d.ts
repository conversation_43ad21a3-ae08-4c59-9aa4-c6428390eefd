import { Response, NextFunction } from 'express';
import { AuthRequest } from '@/types';
export declare class LabResultController {
    static createLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getLabResultById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getPatientLabResults(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getLabResultTrends(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static deleteLabResult(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=labResultController.d.ts.map