"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecurringAppointmentService = void 0;
const client_1 = require("@prisma/client");
const date_fns_1 = require("date-fns");
const errors_1 = require("@/utils/errors");
const appointmentService_1 = require("./appointmentService");
const prisma = new client_1.PrismaClient();
class RecurringAppointmentService {
    static async createRecurringAppointment(data, createdBy, auditData) {
        if (!data.patientId || !data.providerId || !data.startDate || !data.frequency) {
            throw new errors_1.ValidationError('Patient ID, provider ID, start date, and frequency are required');
        }
        if (!['DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', 'YEARLY'].includes(data.frequency)) {
            throw new errors_1.ValidationError('Invalid recurrence frequency');
        }
        const startDate = new Date(data.startDate);
        const endDate = data.endDate ? new Date(data.endDate) : null;
        if (isNaN(startDate.getTime()) || (0, date_fns_1.isBefore)(startDate, new Date())) {
            throw new errors_1.ValidationError('Invalid start date or date is in the past');
        }
        if (endDate && (isNaN(endDate.getTime()) || (0, date_fns_1.isBefore)(endDate, startDate))) {
            throw new errors_1.ValidationError('End date must be after start date');
        }
        if (!endDate && !data.maxOccurrences) {
            throw new errors_1.ValidationError('Either end date or max occurrences must be provided');
        }
        const [patient, provider] = await Promise.all([
            prisma.patient.findFirst({
                where: { id: data.patientId, isDeleted: false },
            }),
            prisma.user.findFirst({
                where: {
                    id: data.providerId,
                    isActive: true,
                    role: { in: ['ADMIN', 'CLINICIAN'] },
                },
            }),
        ]);
        if (!patient) {
            throw new errors_1.NotFoundError('Patient not found');
        }
        if (!provider) {
            throw new errors_1.NotFoundError('Provider not found or not authorized');
        }
        const recurringAppointment = await prisma.recurringAppointment.create({
            data: {
                patientId: data.patientId,
                providerId: data.providerId,
                startDate: startDate,
                endDate: endDate,
                duration: data.duration,
                type: data.type,
                frequency: data.frequency,
                interval: data.interval || 1,
                dayOfWeek: data.dayOfWeek,
                dayOfMonth: data.dayOfMonth,
                timeSlot: data.timeSlot,
                title: data.title?.trim() || null,
                description: data.description?.trim() || null,
                location: data.location?.trim() || null,
                isVirtual: data.isVirtual || false,
                virtualMeetingUrl: data.virtualMeetingUrl?.trim() || null,
                notes: data.notes?.trim() || null,
                maxOccurrences: data.maxOccurrences || null,
                isActive: true,
                createdBy,
            },
            include: {
                patient: true,
                provider: true,
            },
        });
        await this.scheduleAppointmentsForRecurring(recurringAppointment.id);
        await this.createAuditLog({
            userId: createdBy,
            action: 'CREATE',
            entityType: 'RECURRING_APPOINTMENT',
            entityId: recurringAppointment.id,
            patientId: data.patientId,
            newValues: {
                frequency: data.frequency,
                startDate: startDate,
                endDate: endDate,
                maxOccurrences: data.maxOccurrences,
            },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: {
                recurringAppointment: {
                    ...recurringAppointment,
                    patient,
                    provider,
                },
            },
            message: `Created recurring appointment series`,
        };
    }
    static async scheduleAppointmentsForRecurring(id) {
        const recurringAppointment = await prisma.recurringAppointment.findUnique({
            where: { id },
        });
        if (!recurringAppointment)
            return;
        const maxOccurrences = recurringAppointment.maxOccurrences || 52;
        let currentDate = new Date(recurringAppointment.startDate);
        const endDate = recurringAppointment.endDate ? new Date(recurringAppointment.endDate) : null;
        let occurrences = 0;
        while (occurrences < maxOccurrences && (!endDate || currentDate <= endDate)) {
            const existing = await prisma.appointment.findFirst({
                where: {
                    recurringAppointmentId: recurringAppointment.id,
                    date: currentDate,
                },
            });
            if (!existing) {
                await appointmentService_1.AppointmentService.createAppointment({
                    patientId: recurringAppointment.patientId,
                    providerId: recurringAppointment.providerId,
                    date: currentDate.toISOString(),
                    duration: recurringAppointment.duration,
                    type: recurringAppointment.type,
                    title: recurringAppointment.title || undefined,
                    description: recurringAppointment.description || undefined,
                    location: recurringAppointment.location || undefined,
                    isVirtual: recurringAppointment.isVirtual,
                    virtualMeetingUrl: recurringAppointment.virtualMeetingUrl || undefined,
                    notes: `Recurring appointment: ${recurringAppointment.id}`,
                    status: 'SCHEDULED',
                    recurringAppointmentId: recurringAppointment.id,
                }, recurringAppointment.providerId);
            }
            currentDate = this.calculateNextOccurrence(currentDate, recurringAppointment.frequency, recurringAppointment.interval || 1);
            occurrences++;
        }
    }
    static getNextOccurrence(currentDate, pattern, interval) {
        switch (pattern) {
            case 'DAILY':
                return (0, date_fns_1.addDays)(currentDate, interval);
            case 'WEEKLY':
                return (0, date_fns_1.addWeeks)(currentDate, interval);
            case 'BIWEEKLY':
                return (0, date_fns_1.addWeeks)(currentDate, 2 * interval);
            case 'MONTHLY':
                return (0, date_fns_1.addMonths)(currentDate, interval);
            case 'YEARLY':
                return (0, date_fns_1.addYears)(currentDate, interval);
            default:
                throw new Error(`Unsupported recurrence pattern: ${pattern}`);
        }
    }
    static async updateRecurringAppointment(id, data, updateFutureAppointments, userId, userRole, auditData) {
        const existingRecurring = await prisma.recurringAppointment.findFirst({
            where: {
                id,
                ...(userRole === 'CLINICIAN' && { providerId: userId }),
            },
        });
        if (!existingRecurring) {
            throw new errors_1.NotFoundError('Recurring appointment not found');
        }
        const updateData = {};
        if (data.startDate)
            updateData.startDate = new Date(data.startDate);
        if (data.endDate)
            updateData.endDate = new Date(data.endDate);
        if (data.duration)
            updateData.duration = data.duration;
        if (data.type)
            updateData.type = data.type;
        if (data.frequency)
            updateData.frequency = data.frequency;
        if (data.interval)
            updateData.interval = data.interval;
        if (data.dayOfWeek)
            updateData.dayOfWeek = data.dayOfWeek;
        if (data.dayOfMonth)
            updateData.dayOfMonth = data.dayOfMonth;
        if (data.timeSlot)
            updateData.timeSlot = data.timeSlot;
        if (data.notes)
            updateData.notes = data.notes;
        if (data.maxOccurrences)
            updateData.maxOccurrences = data.maxOccurrences;
        if (data.isActive !== undefined)
            updateData.isActive = data.isActive;
        if (data.title !== undefined)
            updateData.title = data.title?.trim() || null;
        if (data.description !== undefined)
            updateData.description = data.description?.trim() || null;
        if (data.location !== undefined)
            updateData.location = data.location?.trim() || null;
        if (data.isVirtual !== undefined)
            updateData.isVirtual = data.isVirtual;
        if (data.virtualMeetingUrl !== undefined)
            updateData.virtualMeetingUrl = data.virtualMeetingUrl?.trim() || null;
        const updatedRecurring = await prisma.recurringAppointment.update({
            where: { id },
            data: updateData,
        });
        await prisma.appointment.deleteMany({
            where: {
                recurringAppointmentId: id,
                date: { gte: new Date() },
            },
        });
        await this.scheduleAppointmentsForRecurring(id);
        const oldValues = {
            startDate: existingRecurring.startDate,
            endDate: existingRecurring.endDate,
            duration: existingRecurring.duration,
            type: existingRecurring.type,
            frequency: existingRecurring.frequency,
        };
        const newValues = {
            startDate: updatedRecurring.startDate,
            endDate: updatedRecurring.endDate,
            duration: updatedRecurring.duration,
            type: updatedRecurring.type,
            frequency: updatedRecurring.frequency,
        };
        await this.createAuditLog({
            userId,
            action: 'UPDATE',
            entityType: 'RECURRING_APPOINTMENT',
            entityId: id,
            patientId: existingRecurring.patientId,
            oldValues,
            newValues,
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: { recurringAppointment: updatedRecurring },
            message: 'Recurring appointment updated successfully',
        };
    }
    static async cancelRecurringAppointment(id, reason, cancelFutureAppointments, userId, userRole, auditData) {
        const recurringAppointment = await prisma.recurringAppointment.findFirst({
            where: {
                id,
                ...(userRole === 'CLINICIAN' && { providerId: userId }),
            },
        });
        if (!recurringAppointment) {
            throw new errors_1.NotFoundError('Recurring appointment not found');
        }
        await prisma.recurringAppointment.update({
            where: { id },
            data: {
                isActive: false,
                notes: recurringAppointment.notes
                    ? `${recurringAppointment.notes}\n\nSeries cancelled: ${reason}`
                    : `Series cancelled: ${reason}`,
            },
        });
        let cancelledCount = 0;
        if (cancelFutureAppointments) {
            const futureAppointments = await prisma.appointment.findMany({
                where: {
                    recurringAppointmentId: id,
                    date: { gte: new Date() },
                    status: { in: ['SCHEDULED', 'CONFIRMED'] },
                    isDeleted: false,
                },
            });
            for (const appointment of futureAppointments) {
                await appointmentService_1.AppointmentService.cancelAppointment(appointment.id, `Recurring series cancelled: ${reason}`, userId, userRole, auditData);
                cancelledCount++;
            }
        }
        await this.createAuditLog({
            userId,
            action: 'CANCEL',
            entityType: 'RECURRING_APPOINTMENT',
            entityId: id,
            patientId: recurringAppointment.patientId,
            newValues: {
                reason,
                cancelledAppointments: cancelledCount,
            },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: { cancelledAppointments: cancelledCount },
            message: `Recurring appointment series cancelled. ${cancelledCount} future appointments cancelled.`,
        };
    }
    static async getRecurringAppointments(query, userId, userRole) {
        const page = parseInt(query.page || '1', 10);
        const limit = Math.min(parseInt(query.limit || '10', 10), 100);
        const skip = (page - 1) * limit;
        const where = {};
        if (userRole === 'CLINICIAN') {
            where.providerId = userId;
        }
        if (query.patientId)
            where.patientId = query.patientId;
        if (query.providerId)
            where.providerId = query.providerId;
        if (query.isActive !== undefined)
            where.isActive = query.isActive === 'true';
        const [recurringAppointments, total] = await Promise.all([
            prisma.recurringAppointment.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    provider: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            username: true,
                        },
                    },
                    appointments: {
                        where: { isDeleted: false },
                        select: {
                            id: true,
                            date: true,
                            status: true,
                        },
                        orderBy: { date: 'asc' },
                    },
                },
            }),
            prisma.recurringAppointment.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: recurringAppointments,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }
    static async createAuditLog(data) {
        const { oldValues, newValues, ...rest } = data;
        await prisma.auditLog.create({
            data: {
                ...rest,
                oldValues: oldValues ? JSON.stringify(oldValues) : null,
                newValues: newValues ? JSON.stringify(newValues) : null,
            },
        });
    }
    static async getRecurringAppointmentById(id) {
        const recurringAppointment = await prisma.recurringAppointment.findUnique({
            where: { id },
            include: {
                patient: {
                    select: { id: true, firstName: true, lastName: true },
                },
                provider: {
                    select: { id: true, firstName: true, lastName: true },
                },
                appointments: {
                    orderBy: { date: 'asc' },
                    take: 5,
                },
            },
        });
        if (!recurringAppointment) {
            throw new errors_1.NotFoundError('Recurring appointment not found');
        }
        return {
            success: true,
            data: { recurringAppointment },
        };
    }
    static async getRecurringAppointmentsByPatient(patientId) {
        const recurringAppointments = await prisma.recurringAppointment.findMany({
            where: { patientId, isDeleted: false },
            include: {
                provider: {
                    select: { id: true, firstName: true, lastName: true },
                },
            },
            orderBy: { startDate: 'desc' },
        });
        return {
            success: true,
            data: { recurringAppointments },
        };
    }
    static async deleteRecurringAppointment(id, userId, auditData) {
        const recurring = await prisma.recurringAppointment.findUnique({ where: { id } });
        if (!recurring) {
            throw new errors_1.NotFoundError('Recurring appointment not found');
        }
        await prisma.recurringAppointment.update({
            where: { id },
            data: { isDeleted: true, isActive: false, deletedAt: new Date() },
        });
        await prisma.appointment.updateMany({
            where: {
                recurringAppointmentId: id,
                date: { gte: new Date() },
                status: { in: ['SCHEDULED', 'CONFIRMED'] },
            },
            data: {
                status: 'CANCELLED',
                notes: 'Parent recurring appointment was deleted.',
            },
        });
        await this.createAuditLog({
            userId,
            action: 'DELETE',
            entityType: 'RECURRING_APPOINTMENT',
            entityId: id,
            patientId: recurring.patientId,
            oldValues: {
                isActive: recurring.isActive,
                isDeleted: recurring.isDeleted,
            },
            newValues: {
                isActive: false,
                isDeleted: true,
            },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            message: 'Recurring appointment deleted successfully',
        };
    }
}
exports.RecurringAppointmentService = RecurringAppointmentService;
//# sourceMappingURL=recurringAppointmentService.js.map