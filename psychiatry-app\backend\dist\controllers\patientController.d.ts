import { Response, NextFunction } from 'express';
import { AuthRequest } from '@/types';
export declare class PatientController {
    static createPatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getPatients(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getPatientById(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static updatePatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static deletePatient(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getPatientStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=patientController.d.ts.map