"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssessmentController = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
const criterionSchema = zod_1.z.object({
    id: zod_1.z.string(),
    code: zod_1.z.string(),
    description: zod_1.z.string(),
    present: zod_1.z.boolean(),
    severity: zod_1.z.number().optional(),
    duration: zod_1.z.string().optional(),
    onset: zod_1.z.string().optional(),
    comments: zod_1.z.string().optional(),
    subCriteria: zod_1.z.array(zod_1.z.lazy(() => criterionSchema)).optional(),
});
const specifierSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    description: zod_1.z.string(),
    selected: zod_1.z.boolean(),
    mutuallyExclusive: zod_1.z.array(zod_1.z.string()).optional(),
});
const disorderAssessmentSchema = zod_1.z.object({
    id: zod_1.z.string().optional(),
    disorderId: zod_1.z.string(),
    patientId: zod_1.z.string(),
    assessmentDate: zod_1.z.string(),
    criteria: zod_1.z.array(criterionSchema),
    specifiers: zod_1.z.array(specifierSchema).optional(),
    severity: zod_1.z.enum(['mild', 'moderate', 'severe', 'unspecified']).optional(),
    courseSpecifiers: zod_1.z.array(zod_1.z.string()).optional(),
    diagnosticConfidence: zod_1.z.enum(['provisional', 'principal', 'rule-out', 'confirmed']).optional(),
    functionalImpairment: zod_1.z.object({
        social: zod_1.z.number(),
        occupational: zod_1.z.number(),
        academic: zod_1.z.number(),
        overall: zod_1.z.number(),
    }).optional(),
    notes: zod_1.z.string().optional(),
    assessorId: zod_1.z.string().optional(),
    status: zod_1.z.enum(['draft', 'completed', 'reviewed']).optional(),
    createdAt: zod_1.z.string().optional(),
    updatedAt: zod_1.z.string().optional(),
});
const riskAssessmentSchema = zod_1.z.object({
    suicidalIdeation: zod_1.z.boolean(),
    homicidalIdeation: zod_1.z.boolean(),
    selfHarm: zod_1.z.boolean(),
    substanceUse: zod_1.z.boolean(),
    level: zod_1.z.enum(['low', 'moderate', 'high', 'imminent']),
    interventions: zod_1.z.array(zod_1.z.string()).optional(),
});
const assessmentSessionSchema = zod_1.z.object({
    patientId: zod_1.z.string(),
    sessionDate: zod_1.z.string(),
    assessments: zod_1.z.array(disorderAssessmentSchema),
    clinicalImpression: zod_1.z.string().optional(),
    treatmentRecommendations: zod_1.z.array(zod_1.z.string()).optional(),
    followUpPlan: zod_1.z.string().optional(),
    riskAssessment: riskAssessmentSchema,
    status: zod_1.z.enum(['in-progress', 'completed', 'reviewed', 'signed']),
    duration: zod_1.z.number(),
    assessorId: zod_1.z.string().optional(),
    supervisorId: zod_1.z.string().optional(),
});
class AssessmentController {
    static async createAssessmentSession(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { patientId } = req.params;
            const validatedData = assessmentSessionSchema.parse({
                ...req.body,
                patientId: patientId || req.body.patientId,
            });
            const result = await prisma.patientAssessment.create({
                data: {
                    patientId: validatedData.patientId,
                    assessorId: req.user.id,
                    sessionDate: new Date(validatedData.sessionDate),
                    assessmentData: JSON.stringify(validatedData),
                    status: validatedData.status,
                    duration: validatedData.duration,
                },
            });
            res.status(201).json({
                success: true,
                data: {
                    id: result.id,
                    ...validatedData,
                    assessorId: req.user.id,
                },
                message: 'Assessment session created successfully',
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getPatientAssessmentSessions(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { patientId } = req.params;
            const assessments = await prisma.patientAssessment.findMany({
                where: {
                    patientId,
                    isDeleted: false,
                },
                orderBy: {
                    sessionDate: 'desc',
                },
            });
            const parsedAssessments = assessments.map(assessment => ({
                id: assessment.id,
                ...JSON.parse(assessment.assessmentData),
                assessorId: assessment.assessorId,
                createdAt: assessment.createdAt,
                updatedAt: assessment.updatedAt,
            }));
            res.status(200).json({
                success: true,
                data: parsedAssessments,
                message: 'Assessment sessions retrieved successfully',
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getAssessmentSessionById(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const assessment = await prisma.patientAssessment.findUnique({
                where: {
                    id,
                    isDeleted: false,
                },
            });
            if (!assessment) {
                res.status(404).json({
                    success: false,
                    error: 'Assessment session not found',
                });
                return;
            }
            const parsedAssessment = {
                id: assessment.id,
                ...JSON.parse(assessment.assessmentData),
                assessorId: assessment.assessorId,
                createdAt: assessment.createdAt,
                updatedAt: assessment.updatedAt,
            };
            res.status(200).json({
                success: true,
                data: parsedAssessment,
                message: 'Assessment session retrieved successfully',
            });
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AssessmentController = AssessmentController;
//# sourceMappingURL=assessmentController.js.map