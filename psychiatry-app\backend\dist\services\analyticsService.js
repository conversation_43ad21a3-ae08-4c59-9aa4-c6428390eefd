"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const client_1 = require("@prisma/client");
const date_fns_1 = require("date-fns");
const prisma = new client_1.PrismaClient();
class AnalyticsService {
    static async getDashboardAnalytics(userId, userRole) {
        const now = new Date();
        const today = (0, date_fns_1.startOfDay)(now);
        const thisWeek = (0, date_fns_1.startOfWeek)(now);
        const thisMonth = (0, date_fns_1.startOfMonth)(now);
        const patientFilter = userRole === 'CLINICIAN' ? { createdBy: userId } : {};
        const appointmentFilter = userRole === 'CLINICIAN' ? { providerId: userId } : {};
        const [totalPatients, newPatientsThisWeek, newPatientsThisMonth, totalAppointments, todayAppointments, thisWeekAppointments, upcomingAppointments, totalLabResults, pendingLabResults, flaggedLabResults, totalUsers, activeUsers,] = await Promise.all([
            prisma.patient.count({
                where: { isDeleted: false, ...patientFilter },
            }),
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: thisWeek },
                    ...patientFilter,
                },
            }),
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: thisMonth },
                    ...patientFilter,
                },
            }),
            prisma.appointment.count({
                where: { isDeleted: false, ...appointmentFilter },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    date: { gte: today, lt: (0, date_fns_1.endOfDay)(now) },
                    status: { in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'] },
                    ...appointmentFilter,
                },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    date: { gte: thisWeek, lt: (0, date_fns_1.endOfWeek)(now) },
                    ...appointmentFilter,
                },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    date: { gte: now },
                    status: { in: ['SCHEDULED', 'CONFIRMED'] },
                    ...appointmentFilter,
                },
            }),
            prisma.labResult.count({
                where: { isDeleted: false },
            }),
            prisma.labResult.count({
                where: {
                    isDeleted: false,
                    status: { in: ['PENDING', 'IN_PROGRESS'] },
                },
            }),
            prisma.labResult.count({
                where: {
                    isDeleted: false,
                    flags: { not: null },
                },
            }),
            userRole === 'ADMIN' ? prisma.user.count() : 0,
            userRole === 'ADMIN' ? prisma.user.count({ where: { isActive: true } }) : 0,
        ]);
        const analytics = {
            patients: {
                total: totalPatients,
                newThisWeek: newPatientsThisWeek,
                newThisMonth: newPatientsThisMonth,
                growthRate: totalPatients > 0 ? (newPatientsThisMonth / totalPatients) * 100 : 0,
            },
            appointments: {
                total: totalAppointments,
                today: todayAppointments,
                thisWeek: thisWeekAppointments,
                upcoming: upcomingAppointments,
            },
            labResults: {
                total: totalLabResults,
                pending: pendingLabResults,
                flagged: flaggedLabResults,
                flaggedPercentage: totalLabResults > 0 ? (flaggedLabResults / totalLabResults) * 100 : 0,
            },
            system: userRole === 'ADMIN' ? {
                totalUsers,
                activeUsers,
                activePercentage: totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0,
            } : null,
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getPatientAnalytics(dateRange, userId, userRole) {
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const patientFilter = userRole === 'CLINICIAN' ? { createdBy: userId } : {};
        const [genderDistribution, ageGroups, registrationTrend, patientsWithAppointments, patientsWithLabResults, stateDistribution,] = await Promise.all([
            prisma.patient.groupBy({
                by: ['gender'],
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    ...patientFilter,
                },
                _count: { gender: true },
            }),
            prisma.$queryRaw `
        SELECT
          CASE
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) < 18 THEN 'Under 18'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 18 AND 30 THEN '18-30'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 31 AND 50 THEN '31-50'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 51 AND 70 THEN '51-70'
            ELSE 'Over 70'
          END as age_group,
          COUNT(*) as count
        FROM "patients"
        WHERE "isDeleted" = false
          AND "createdAt" >= ${fromDate}
          AND "createdAt" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND "createdBy" = ${userId}` : prisma.$queryRaw ``}
        GROUP BY age_group
        ORDER BY age_group
      `,
            prisma.$queryRaw `
        SELECT
          DATE("createdAt") as date,
          COUNT(*) as count
        FROM "patients"
        WHERE "isDeleted" = false
          AND "createdAt" >= ${fromDate}
          AND "createdAt" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND "createdBy" = ${userId}` : prisma.$queryRaw ``}
        GROUP BY DATE("createdAt")
        ORDER BY date
      `,
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    appointments: { some: { isDeleted: false } },
                    ...patientFilter,
                },
            }),
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    labResults: { some: { isDeleted: false } },
                    ...patientFilter,
                },
            }),
            prisma.patient.groupBy({
                by: ['address'],
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    address: { not: null },
                    ...patientFilter,
                },
                _count: { address: true },
            }),
        ]);
        const totalPatients = await prisma.patient.count({
            where: {
                isDeleted: false,
                createdAt: { gte: fromDate, lte: toDate },
                ...patientFilter,
            },
        });
        const analytics = {
            demographics: {
                gender: genderDistribution.reduce((acc, item) => {
                    acc[item.gender] = item._count.gender;
                    return acc;
                }, {}),
                ageGroups: ageGroups,
            },
            trends: {
                registration: registrationTrend,
            },
            engagement: {
                totalPatients,
                withAppointments: patientsWithAppointments,
                withLabResults: patientsWithLabResults,
                appointmentRate: totalPatients > 0 ? (patientsWithAppointments / totalPatients) * 100 : 0,
                labResultRate: totalPatients > 0 ? (patientsWithLabResults / totalPatients) * 100 : 0,
            },
            geographic: {
                states: stateDistribution.slice(0, 10),
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getAppointmentAnalytics(dateRange, userId, userRole) {
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const appointmentFilter = userRole === 'CLINICIAN' ? { providerId: userId } : {};
        const [statusDistribution, typeDistribution, dailyTrend, providerStats, durationStats, cancellationStats,] = await Promise.all([
            prisma.appointment.groupBy({
                by: ['status'],
                where: {
                    isDeleted: false,
                    date: { gte: fromDate, lte: toDate },
                    ...appointmentFilter,
                },
                _count: { status: true },
            }),
            prisma.appointment.groupBy({
                by: ['type'],
                where: {
                    isDeleted: false,
                    date: { gte: fromDate, lte: toDate },
                    ...appointmentFilter,
                },
                _count: { type: true },
            }),
            prisma.$queryRaw `
        SELECT
          DATE(date) as day,
          COUNT(*) as count
        FROM appointments
        WHERE is_deleted = false
          AND date >= ${fromDate}
          AND date <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND provider_id = ${userId}` : prisma.$queryRaw ``}
        GROUP BY day
        ORDER BY day
      `,
            userRole === 'ADMIN' ? prisma.$queryRaw `
        SELECT 
          u.first_name,
          u.last_name,
          COUNT(a.id) as total_appointments,
          COUNT(CASE WHEN a.status = 'COMPLETED' THEN 1 END) as completed,
          COUNT(CASE WHEN a.status = 'CANCELLED' THEN 1 END) as cancelled,
          AVG(a.duration) as avg_duration
        FROM users u
        LEFT JOIN appointments a ON u.id = a.provider_id 
          AND a.is_deleted = false 
          AND a.date >= ${fromDate}
          AND a.date <= ${toDate}
        WHERE u.role IN ('ADMIN', 'CLINICIAN')
          AND u.is_active = true
        GROUP BY u.id, u.first_name, u.last_name
        ORDER BY total_appointments DESC
      ` : [],
            prisma.appointment.aggregate({
                where: {
                    isDeleted: false,
                    date: { gte: fromDate, lte: toDate },
                    status: 'COMPLETED',
                    ...appointmentFilter,
                },
                _avg: { duration: true },
                _min: { duration: true },
                _max: { duration: true },
            }),
            prisma.$queryRaw `
        SELECT 
          EXTRACT(DOW FROM date) as day_of_week,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled
        FROM appointments 
        WHERE is_deleted = false 
          AND date >= ${fromDate}
          AND date <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND provider_id = ${userId}` : prisma.$queryRaw ``}
        GROUP BY EXTRACT(DOW FROM date)
        ORDER BY day_of_week
      `,
        ]);
        const totalAppointments = await prisma.appointment.count({
            where: {
                isDeleted: false,
                date: { gte: fromDate, lte: toDate },
                ...appointmentFilter,
            },
        });
        const analytics = {
            overview: {
                total: totalAppointments,
                statusDistribution: statusDistribution.reduce((acc, item) => {
                    acc[item.status] = item._count.status;
                    return acc;
                }, {}),
                typeDistribution: typeDistribution.reduce((acc, item) => {
                    acc[item.type] = item._count.type;
                    return acc;
                }, {}),
            },
            trends: {
                daily: dailyTrend,
            },
            performance: {
                providers: userRole === 'ADMIN' ? providerStats : null,
                duration: durationStats,
                cancellationByDay: cancellationStats,
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getLabResultAnalytics(dateRange, userId, userRole) {
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const [testTypeDistribution, statusDistribution, flaggedResults, turnaroundStats, monthlyTrend,] = await Promise.all([
            prisma.labResult.groupBy({
                by: ['testType'],
                where: {
                    isDeleted: false,
                    testDate: { gte: fromDate, lte: toDate },
                },
                _count: { testType: true },
            }),
            prisma.labResult.groupBy({
                by: ['status'],
                where: {
                    isDeleted: false,
                    testDate: { gte: fromDate, lte: toDate },
                },
                _count: { status: true },
            }),
            prisma.$queryRaw `
        SELECT 
          test_type,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' THEN 1 END) as flagged
        FROM lab_results 
        WHERE is_deleted = false 
          AND test_date >= ${fromDate}
          AND test_date <= ${toDate}
        GROUP BY test_type
        ORDER BY flagged DESC
      `,
            prisma.$queryRaw `
        SELECT 
          AVG(EXTRACT(EPOCH FROM (created_at - test_date))/3600) as avg_hours,
          MIN(EXTRACT(EPOCH FROM (created_at - test_date))/3600) as min_hours,
          MAX(EXTRACT(EPOCH FROM (created_at - test_date))/3600) as max_hours
        FROM lab_results 
        WHERE is_deleted = false 
          AND test_date >= ${fromDate}
          AND test_date <= ${toDate}
          AND status = 'COMPLETED'
      `,
            prisma.$queryRaw `
        SELECT 
          DATE_TRUNC('month', test_date) as month,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' THEN 1 END) as flagged
        FROM lab_results 
        WHERE is_deleted = false 
          AND test_date >= ${fromDate}
          AND test_date <= ${toDate}
        GROUP BY DATE_TRUNC('month', test_date)
        ORDER BY month
      `,
        ]);
        const totalLabResults = await prisma.labResult.count({
            where: {
                isDeleted: false,
                testDate: { gte: fromDate, lte: toDate },
            },
        });
        const analytics = {
            overview: {
                total: totalLabResults,
                testTypeDistribution: testTypeDistribution.reduce((acc, item) => {
                    acc[item.testType] = item._count.testType;
                    return acc;
                }, {}),
                statusDistribution: statusDistribution.reduce((acc, item) => {
                    acc[item.status] = item._count.status;
                    return acc;
                }, {}),
            },
            quality: {
                flaggedByTestType: flaggedResults,
                turnaroundTime: turnaroundStats[0] || { avg_hours: 0, min_hours: 0, max_hours: 0 },
            },
            trends: {
                monthly: monthlyTrend,
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getSystemAnalytics(dateRange, userId, userRole) {
        if (userRole !== 'ADMIN') {
            return {
                success: false,
                error: 'Access denied. Admin role required.',
            };
        }
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const [userActivity, auditSummary, usageTrends, errorAnalysis,] = await Promise.all([
            prisma.$queryRaw `
        SELECT 
          u.role,
          COUNT(DISTINCT u.id) as total_users,
          COUNT(DISTINCT CASE WHEN u.last_login >= ${(0, date_fns_1.subDays)(new Date(), 7)} THEN u.id END) as active_last_7_days,
          COUNT(DISTINCT CASE WHEN u.last_login >= ${(0, date_fns_1.subDays)(new Date(), 30)} THEN u.id END) as active_last_30_days
        FROM users u
        WHERE u.is_active = true
        GROUP BY u.role
      `,
            prisma.auditLog.groupBy({
                by: ['action', 'entityType'],
                where: {
                    timestamp: { gte: fromDate, lte: toDate },
                },
                _count: { action: true },
            }),
            prisma.$queryRaw `
        SELECT 
          DATE("timestamp") as date,
          "entityType",
          "action",
          COUNT(*) as count
        FROM "audit_logs" 
        WHERE "timestamp" >= ${fromDate}
          AND "timestamp" <= ${toDate}
        GROUP BY DATE("timestamp"), "entityType", "action"
        ORDER BY date
      `,
            [],
        ]);
        const analytics = {
            users: {
                activity: userActivity,
            },
            audit: {
                summary: auditSummary.reduce((acc, item) => {
                    const key = `${item.entityType}_${item.action}`;
                    acc[key] = item._count.action;
                    return acc;
                }, {}),
            },
            usage: {
                trends: usageTrends,
            },
            errors: {
                analysis: errorAnalysis,
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
}
exports.AnalyticsService = AnalyticsService;
//# sourceMappingURL=analyticsService.js.map